import * as registries from "./registries.js";
import * as fs from "node:fs";

export abstract class gameComponet<RecordShaping extends {name:string}>{
    public static empty:gameComponet<any>;
    public abstract ToJSON():RecordShaping;
    public name:string = "";
}
class X extends gameComponet<{name:string}>{
    public ToJSON():{name:string}{
        return {name:this.name};
    }
}
export function key(a:[string, string]):string{
    return a.join(":");
}
export class RegistryFunc{
    public static all: (()=>Promise<null>)[] = [];
    constructor(public func:()=>Promise<null>){
        RegistryFunc.all.push(func);
    }
    static async run(){
        let promised:Promise<null>[] = []
        RegistryFunc.all.forEach((value)=>{
            promised.push(value());
        })
        await Promise.all(promised);
        let all = registries.registry.all;
        let ou:Record<string, any[]> = {}
        for(let i = 0; i < all.length; i++){
            ou[all[i].type] = all[i].toArray()
        }
        let out:string = JSON.stringify(registries.registry.all)
        fs.writeFileSync("out.json", out);
    }
}
gameComponet.empty = new X();
type NonFunctionKeys<T> = {
    [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];
export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>