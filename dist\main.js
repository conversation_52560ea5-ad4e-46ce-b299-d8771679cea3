// Combined TypeScript file - generated by tsBind.py
import * as fs from 'node:fs';
// ===== core.ts =====
var core;
(function (core) {
    class gameComponet {
        static empty;
        name = "";
    }
    core.gameComponet = gameComponet;
    class X extends gameComponet {
        ToJSON() {
            return { name: this.name };
        }
    }
    function key(a) {
        return a.join(":");
    }
    core.key = key;
    class RegistryFunc {
        func;
        static all = [];
        constructor(func) {
            this.func = func;
            RegistryFunc.all.push(func);
        }
        static async run() {
            let promised = [];
            RegistryFunc.all.forEach((value) => {
                promised.push(value());
            });
            await Promise.all(promised);
            let all = registries.registry.all;
            let ou = {};
            for (let i = 0; i < all.length; i++) {
                ou[all[i].type] = all[i].toArray();
            }
            let out = JSON.stringify(registries.registry.all);
            fs.writeFileSync("out.json", out);
        }
    }
    core.RegistryFunc = RegistryFunc;
    gameComponet.empty = new X();
})(core || (core = {}));
// ===== registries.ts =====
var registries;
(function (registries) {
    class registry {
        type;
        space;
        static all = {};
        registry = {};
        constructor(type, space) {
            this.type = type;
            this.space = space;
            if (registry.all[this.type] == undefined) {
                registry.all[this.type] = [];
            }
            registry.all[this.type].push(this);
        }
        get(what) {
            return this.registry[core.key([this.space, what])];
        }
        add(value) {
            this.registry[core.key([this.space, value.name])] = value;
            return value;
        }
        toArray() {
            let a = [];
            Array.from(Object.keys(this.registry)).forEach((value, index) => {
                a[index] = this.registry[value].ToJSON();
                a[index].name = this.space + ":" + a[index].name;
            });
            return a;
        }
    }
    registries.registry = registry;
})(registries || (registries = {}));
// ===== block.ts =====
var block;
(function (block_1) {
    class blockProperties {
        flammable = false;
        Flamable(a) {
            this.flammable = a;
            return this;
        }
        hitbox = [[[0, 0, 0], [16, 16, 16]]];
        Hitbox(a) {
            this.hitbox = a;
            return this;
        }
    }
    block_1.blockProperties = blockProperties;
    class block extends core.gameComponet {
        behaviour;
        properties;
        attachedComponent;
        static GUI = { name: "gui" };
        static block = { name: "block" };
        static pillarBlock = { name: "pillar_block" };
        ToJSON() {
            return {
                properties: this.properties,
                name: this.name
            };
        }
        constructor(name, behaviour, properties, attachedComponent) {
            super();
            this.behaviour = behaviour;
            this.properties = properties;
            this.attachedComponent = attachedComponent;
            this.name = name;
        }
    }
    block_1.block = block;
    class blockRegistry extends registries.registry {
        constructor(space) {
            super("block", space);
        }
    }
    block_1.blockRegistry = blockRegistry;
    block_1.f = new core.RegistryFunc(async () => {
        let blockReg = new blockRegistry("_");
        const airBlock = blockReg.add(new block("_", block.block, new blockProperties().Hitbox([[[0, 0, 0], [0, 0, 0]]]), null));
        const bedrockBlock = blockReg.add(new block("bedrock", block.block, new blockProperties(), null));
        console.log(blockReg);
        return null;
    });
})(block || (block = {}));
// ===== item.ts =====
var item;
(function (item) {
    class ItemProerties {
        flammable = true;
        Flamable(a) {
            this.flammable = a;
            return this;
        }
    }
    item.ItemProerties = ItemProerties;
    class Item extends core.gameComponet {
        behaviour;
        static Item = { name: "item" };
        static BlockItem = { name: "block_item" };
        properties;
        attachedComponent;
        constructor(name, behaviour, properties, attachedComponent) {
            super();
            this.behaviour = behaviour;
            this.name = name;
            this.properties = properties;
            this.attachedComponent = attachedComponent;
            if (attachedComponent == undefined) {
                this.attachedComponent = core.gameComponet.empty;
            }
        }
        ToJSON() {
            return {
                name: this.name,
                properties: this.properties,
                behaviour: this.behaviour,
                attachedComponent: this.attachedComponent.ToJSON()
            };
        }
    }
    item.Item = Item;
    class ItemRegistry extends registries.registry {
        constructor(space) {
            super("item", space);
        }
    }
    item.ItemRegistry = ItemRegistry;
    item.f = new core.RegistryFunc(async () => {
        let itemRegistry = new ItemRegistry("_");
        item.bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem, new ItemProerties(), block.bedrockBlock));
        item.air = itemRegistry.add(new Item("_", Item.BlockItem, new ItemProerties(), core.gameComponet.empty));
        return null;
    });
})(item || (item = {}));
// ===== main.ts =====
await core.RegistryFunc.run();
