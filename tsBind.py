import os
import re

ts_files = [
    "src/data/core.ts",
    "src/data/registries.ts",
    "src/data/block.ts",
    "src/data/item.ts",
    "src/data/main.ts",
]

out_file = "dist/temp/main.ts"

# Predefined imports to use instead of collecting from files
all_imports = {
    "import * as fs from 'node:fs';"
}
file_contents = {}
def remove_imports(code: str) -> str:
    # Remove ES6 imports
    code = re.sub(r'^\s*import\s+.*?;\s*$', '', code, flags=re.MULTILINE)
    # Remove CommonJS requires
    code = re.sub(r'^\s*const\s+\w+\s*=\s*require\(.*?\);\s*$', '', code, flags=re.MULTILINE)
    code = re.sub(r'^\s*import\s+.*?;\s*$', '', content, flags=re.MULTILINE)
    return code
# First pass: collect all imports and file contents
for file in ts_files:
    with open(file, "r") as f:
        content = f.read()
        file_contents[file] = content
        
        # Find all import statements
        import_lines = re.findall(r'^import.*?;$', content, re.MULTILINE)

# Second pass: remove imports from each file
for file in ts_files:
    content = file_contents[file]
    # Remove import statements - improved regex to catch more variations
    content = remove_imports(content)
    # Remove empty lines at the beginning
    content = re.sub(r'^\s*\n', '', content)
    file_contents[file] = content

# Create the combined file
combined_content = "// Combined TypeScript file - generated by tsBind.py\n\n"

# Add all unique imports at the top
combined_content += "\n".join(sorted(all_imports)) + "\n\n"

# Add the content of each file wrapped in a namespace
for file in ts_files:
    filename = os.path.basename(file)
    namespace_name = os.path.splitext(filename)[0]  # Remove the extension
    
    # Skip wrapping main.mts in a namespace
    if namespace_name == "main":
        combined_content += f"\n// ===== {filename} =====\n\n"
        combined_content += file_contents[file] + "\n"
    else:
        combined_content += f"\n// ===== {filename} =====\n\n"
        combined_content += f"namespace {namespace_name} {{\n"
        # Indent the content
        indented_content = "    " + file_contents[file].replace("\n", "\n    ")
        combined_content += indented_content + "\n"
        combined_content += "}\n"

# Write the combined file
with open(out_file, "w") as f:
    f.write(combined_content)

print(f"Combined TypeScript file written to {out_file}")
