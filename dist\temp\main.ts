// Combined TypeScript file - generated by tsBind.py

import * as fs from 'node:fs';


// ===== core.ts =====

namespace core {
    export abstract class gameComponet<RecordShaping extends {name:string}>{
        public static empty:gameComponet<any>;
        public abstract ToJSON():RecordShaping;
        public name:string = "";
    }
    class X extends gameComponet<{name:string}>{
        public ToJSON():{name:string}{
            return {name:this.name};
        }
    }
    export function key(a:[string, string]):string{
        return a.join(":");
    }
    export class RegistryFunc{
        public static all: (()=>Promise<null>)[] = [];
        constructor(public func:()=>Promise<null>){
            RegistryFunc.all.push(func);
        }
        static async run(){
            let promised:Promise<null>[] = []
            RegistryFunc.all.forEach((value)=>{
                promised.push(value());
            })
            await Promise.all(promised);
            let all = registries.registry.all;
            let ou:Record<string, any[]> = {}
            for(let i = 0; i < all.length; i++){
                ou[all[i].type] = all[i].toArray()
            }
            let out:string = JSON.stringify(registries.registry.all)
            fs.writeFileSync("out.json", out);
        }
    }
    gameComponet.empty = new X();
    type NonFunctionKeys<T> = {
        [K in keyof T]: T[K] extends Function ? never : K;
    }[keyof T];
    export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>
}

// ===== registries.ts =====

namespace registries {
    export abstract class registry<T extends core.gameComponet<U>, U extends {name:string}>{
        static all: Record<string, registry<any, any>[]> = {};
        protected registry: Record<string, T> = {};
        constructor(protected type:string, protected space:string){
            if(registry.all[this.type] == undefined){
                registry.all[this.type] = [];
            }
            registry.all[this.type].push(this);
        }
        public get(what:string):T{
            return this.registry[core.key([this.space, what])] as T;
        }
        public add(value:T):T{
            this.registry[core.key([this.space, value.name])] = value;
            return value;
        }
        public toArray():U[]{
            let a:U[] = [];
            Array.from(Object.keys(this.registry)).forEach((value, index)=>{
                a[index] = this.registry[value].ToJSON();
                a[index].name = this.space + ":" + a[index].name;
            })
            return a
        }
    }
}

// ===== block.ts =====

namespace block {
    type point = [number, number, number]&{length:3};
    type box = ([point, point ]&{length:2})
    export class blockProperties{
        public flammable:boolean = false;
        Flamable(a:boolean){
            this.flammable = a;
            return this;
        }
        hitbox: box[] = [[[0,0,0],[16,16,16]]]
        Hitbox(a:box[]){
            this.hitbox = a;
            return this;
        }
    }
    export type blockJSON = {
        properties:core.NonFunctionProperties<blockProperties>;
        name:string
    };
    export type blockBehaviour = {name:string}
    export type blockBehabiourProcessor<T extends blockBehaviour> = 
        T extends block.GUI? block<any> : 
        (undefined|null);
    export namespace block{
        export type GUI = {name:"gui"};
        export type block = {name:"block"};
        export type pillarBlock = {name:"pillar_block"};
    }
    export class block<T extends blockBehaviour> extends core.gameComponet<blockJSON>{
        static readonly GUI:blockBehaviour = {name:"gui"};
        static readonly block:block.block = {name:"block"};
        static readonly pillarBlock:block.pillarBlock = {name:"pillar_block"};
        public ToJSON():blockJSON{
            return {
                properties:this.properties,
                name:this.name
            };
        }
        constructor(name:string, public behaviour:T, public properties:blockProperties, public attachedComponent:blockBehabiourProcessor<T>){
            super();
            this.name = name;
        }
    }
    export class blockRegistry extends registries.registry<block<any>, blockJSON>{
        constructor(space:string){
            super("block", space);
        }
    }
    export let airBlock:block<block.block>;
    export let bedrockBlock:block<block.block>;
    export let f = new core.RegistryFunc(async ()=>{
        let blockReg: blockRegistry = new blockRegistry("_");
        const airBlock = blockReg.add(new block("_", block.block,new blockProperties().Hitbox([[[0,0,0],[0,0,0]]]), null));  
        const bedrockBlock = blockReg.add(new block("bedrock", block.block, new blockProperties(),null));
        console.log(blockReg);
        return null
    });
}

// ===== item.ts =====

namespace item {
    export class ItemProerties{
        public flammable:boolean = true;
        Flamable(a:boolean){
            this.flammable = a;
            return this;
        }
    }
    export type ItemBehaviour = {name:string}
    export type ItemJSON = {
        properties: core.NonFunctionProperties<ItemProerties>;
        name: string;
        behaviour: ItemBehaviour;
        attachedComponent: core.gameComponet<any>;
    }
    export type itemBehaviourProcesor<T extends ItemBehaviour> = 
        T extends {name:"block_item"} ? block.block<any> : 
        core.gameComponet<any>;
    export namespace Item{
        export type Item = {name:"item"};
        export type BlockItem = {name:"block_item"};
    }
    export class Item<V extends ItemBehaviour> extends core.gameComponet<ItemJSON>{
        static readonly Item:ItemBehaviour = {name:"item"};
        static readonly BlockItem:ItemBehaviour = {name:"block_item"};
        public properties: ItemProerties;
        public attachedComponent:itemBehaviourProcesor<ItemBehaviour>;
        constructor(name:string, public behaviour:V, properties:ItemProerties, attachedComponent:itemBehaviourProcesor<V>){
            super();
            this.name = name;
            this.properties = properties;
            this.attachedComponent = attachedComponent as core.gameComponet<any>;
            if(attachedComponent == undefined){
                this.attachedComponent = core.gameComponet.empty;
            }
        }
        public ToJSON() {
            return {
                name: this.name,
                properties: this.properties,
                behaviour: this.behaviour,
                attachedComponent: this.attachedComponent.ToJSON()
            }
        }
    }
    export class ItemRegistry extends registries.registry<Item<any>, ItemJSON>{
        constructor(space:string){
            super("item", space);
        }
    }
    export let bedrock:Item<Item.BlockItem>;
    export let air:Item<Item.BlockItem>;
    export let f = new core.RegistryFunc(async ()=>{
        let itemRegistry: ItemRegistry = new ItemRegistry("_");
        bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem ,new ItemProerties(), block.bedrockBlock));  
        air = itemRegistry.add(new Item("_", Item.BlockItem, new ItemProerties(), core.gameComponet.empty));
        return null
    });
}

// ===== main.ts =====

await core.RegistryFunc.run()
