import * as core from "./core.js";
import * as registries from "./registries.js";

type point = [number, number, number]&{length:3};
type box = ([point, point ]&{length:2})
export class blockProperties{
    public flammable:boolean = false;
    Flamable(a:boolean){
        this.flammable = a;
        return this;
    }
    hitbox: box[] = [[[0,0,0],[16,16,16]]]
    Hitbox(a:box[]){
        this.hitbox = a;
        return this;
    }
}
export type blockJSON = {
    properties:core.NonFunctionProperties<blockProperties>;
    name:string
};
export type blockBehaviour = {name:string}
export type blockBehabiourProcessor<T extends blockBehaviour> = 
    T extends block.GUI? block<any> : 
    (undefined|null);
export namespace block{
    export type GUI = {name:"gui"};
    export type block = {name:"block"};
    export type pillarBlock = {name:"pillar_block"};
}
export class block<T extends blockBehaviour> extends core.gameComponet<blockJSON>{
    static readonly GUI:blockBehaviour = {name:"gui"};
    static readonly block:block.block = {name:"block"};
    static readonly pillarBlock:block.pillarBlock = {name:"pillar_block"};
    public ToJSON():blockJSON{
        return {
            properties:this.properties,
            name:this.name
        };
    }
    constructor(name:string, public behaviour:T, public properties:blockProperties, public attachedComponent:blockBehabiourProcessor<T>){
        super();
        this.name = name;
    }
}
export class blockRegistry extends registries.registry<block<any>, blockJSON>{
    constructor(space:string){
        super("block", space);
    }
}
export let airBlock:block<block.block>;
export let bedrockBlock:block<block.block>;
export let f = new core.RegistryFunc(async ()=>{
    let blockReg: blockRegistry = new blockRegistry("_");
    const airBlock = blockReg.add(new block("_", block.block,new blockProperties().Hitbox([[[0,0,0],[0,0,0]]]), null));  
    const bedrockBlock = blockReg.add(new block("bedrock", block.block, new blockProperties(),null));
    console.log(blockReg);
    return null
});